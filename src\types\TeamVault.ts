import { folderSchema } from "@/schema/folder";
import { z } from "zod";

export type FolderModalProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  buttonText: string;
  initialValue?: string;
  onSubmit: (data: FolderFormData) => Promise<void>;
  loading: boolean;
};

export type Folder = {
  id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  parentId?: string;
};

export type FolderFormData = z.infer<typeof folderSchema>;

export type TeamVaultItem = {
  _id: string;
  url: string;
  name: string;
  mimeType: string;
  uploadedAt: Date;
  folderId?: string;
  itemsCount?: number;
};
