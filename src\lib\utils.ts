import { auth } from "@/app/firebase";
import { clsx, type ClassValue } from "clsx";
import { addDays } from "date-fns";
import { getIdToken, onAuthStateChanged } from "firebase/auth";
import { twMerge } from "tailwind-merge";
import Cookies from "js-cookie";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const setToStartOfDay = (date: Date) => {
  return new Date(date.setHours(0, 0, 0, 0));
};

export const checkIsStartDateLowerThanOrEqualsToEndDate = (
  startDate: Date,
  endDate: Date,
) => {
  const startOfDayStart = setToStartOfDay(startDate);
  const startOfDayEnd = setToStartOfDay(endDate);
  const result = startOfDayStart <= startOfDayEnd;
  return result;
};

export const calculateDurationFromStartAndEndDate = (
  startDate: Date,
  endDate: Date,
) => {
  const diffTime = endDate.getTime() - startDate.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

export const checkIfStringIsWholeNunber = (val: string) => {
  const num = Number(val);
  const isWholeNUmber = Number.isInteger(num) && num >= 0;
  console.log(num, isWholeNUmber);
  return isWholeNUmber;
};

export const calculateEndDate = (startDate: string, durationDays: number) => {
  const endDate = addDays(startDate, durationDays);
  return endDate;
};

export const getToken = async (): Promise<string | null> => {
  return new Promise((resolve) => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        const token = await getIdToken(user);
        unsubscribe();
        resolve(token);
      } else {
        unsubscribe();
        resolve(null);
      }
    });
  });
};

export const downloadFile = async (file: string, name: string) => {
  const response = await fetch(file);
  const blob = await response.blob();
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = name;
  link.click();
  window.URL.revokeObjectURL(url);
};

export function getIsTeamMemberFromCookies(): boolean | null {
  const value = Cookies.get("isTeamMember");
  if (value === undefined) return null;
  return value === "true";
}

export function getIsAdminFromCookies(): boolean | null {
  const value = Cookies.get("isAdmin");
  if (value === undefined) return null;
  return value === "true";
}

export function formatDateRange(startDate: string, endDate: string): string {
  const options: Intl.DateTimeFormatOptions = {
    day: "2-digit",
    month: "short",
  };
  const start = new Date(startDate).toLocaleDateString("en-US", options);
  const end = new Date(endDate).toLocaleDateString("en-US", options);
  return `${start} - ${end}`;
}

export function formatDateForDueDate(dateString: string): string {
  const options: Intl.DateTimeFormatOptions = {
    day: "2-digit",
    month: "short",
  };
  return new Date(dateString).toLocaleDateString("en-US", options);
}

// Chat date utilities
export function formatChatDate(dateString: string): string {
  const date = new Date(dateString);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  // Reset time to compare only dates
  const messageDate = new Date(
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
  );
  const todayDate = new Date(
    today.getFullYear(),
    today.getMonth(),
    today.getDate(),
  );
  const yesterdayDate = new Date(
    yesterday.getFullYear(),
    yesterday.getMonth(),
    yesterday.getDate(),
  );

  if (messageDate.getTime() === todayDate.getTime()) {
    return "Today";
  } else if (messageDate.getTime() === yesterdayDate.getTime()) {
    return "Yesterday";
  } else {
    const options: Intl.DateTimeFormatOptions = {
      day: "2-digit",
      month: "short",
      year: "numeric",
    };
    return date.toLocaleDateString("en-US", options);
  }
}

export function isSameDay(date1: string, date2: string): boolean {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  return (
    d1.getFullYear() === d2.getFullYear() &&
    d1.getMonth() === d2.getMonth() &&
    d1.getDate() === d2.getDate()
  );
}

export function shouldShowDateSeparator(
  currentMessage: { timestamp: string },
  previousMessage: { timestamp: string } | null,
): boolean {
  if (!previousMessage) return true;
  return !isSameDay(currentMessage.timestamp, previousMessage.timestamp);
}

// function to get file category from mime type
export function getFileCategory(mimeType: string) {
  if (!mimeType) return "unknown";

  if (mimeType.startsWith("image/")) return "image";
  if (mimeType.startsWith("audio/")) return "audio";
  if (mimeType.startsWith("video/")) return "video";
  if (mimeType === "application/pdf") return "pdf";

  const documentTypes = [
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.ms-powerpoint",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    "text/plain",
    "text/csv",
  ];

  if (documentTypes.includes(mimeType)) return "docs";

  return "other";
}
