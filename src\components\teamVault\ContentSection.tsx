"use client";
import React from "react";
import DynamicItems from "./DynamicItems";
import { TeamVaultItem } from "@/types/TeamVault";

function ContentSection() {
  // Dummy data for testing DynamicItems component
  const dummyItems: TeamVaultItem[] = [
    // Folder items
    {
      _id: "folder1",
      url: "",
      name: "Project Documents",
      mimeType: "folder",
      uploadedAt: new Date("2023-07-11"),
      itemsCount: 12,
    },
    {
      _id: "folder2",
      url: "",
      name: "Design Files",
      mimeType: "folder",
      uploadedAt: new Date("2023-07-10"),
      itemsCount: 8,
    },
    {
      _id: "folder3",
      url: "",
      name: "Construction Plans",
      mimeType: "folder",
      uploadedAt: new Date("2023-07-09"),
      itemsCount: 15,
    },

    // File items
    {
      _id: "file1",
      url: "https://example.com/document.pdf",
      name: "Project_Proposal.pdf",
      mimeType: "application/pdf",
      uploadedAt: new Date("2023-07-11"),
    },
    {
      _id: "file2",
      url: "https://example.com/image.jpg",
      name: "site_photo.jpg",
      mimeType: "image/jpeg",
      uploadedAt: new Date("2023-07-10"),
    },
    {
      _id: "file3",
      url: "https://example.com/video.mp4",
      name: "construction_progress.mp4",
      mimeType: "video/mp4",
      uploadedAt: new Date("2023-07-09"),
    },
    {
      _id: "file4",
      url: "https://example.com/audio.mp3",
      name: "meeting_recording.mp3",
      mimeType: "audio/mpeg",
      uploadedAt: new Date("2023-07-08"),
    },
    {
      _id: "file5",
      url: "https://example.com/document.docx",
      name: "specifications.docx",
      mimeType:
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      uploadedAt: new Date("2023-07-07"),
    },
  ];

  return (
    <div className="">
      <div className="grid grid-cols-5  gap-6">
        {dummyItems.map((item) => (
          <DynamicItems key={item._id} item={item} />
        ))}
      </div>
    </div>
  );
}

export default ContentSection;
