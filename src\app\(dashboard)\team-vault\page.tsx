import PageHeader from "@/components/layout/pageHeader/PageHeader";
import AddNewItem from "@/components/teamVault/AddNewItem";
import ContentSection from "@/components/teamVault/ContentSection";

function page() {
  return (
    <div className="flex flex-col h-screen">
      <PageHeader>
        <div className="flex flex-col">
          <PageHeader.Heading>Team Vault</PageHeader.Heading>
          <PageHeader.Description>
            Access shared files and documents across your organization. All
            files are securely stored and permission-controlled.
          </PageHeader.Description>
        </div>
        <AddNewItem />
      </PageHeader>
      <div className="flex-1 overflow-y-auto p-4 mt-5">
        <ContentSection />
      </div>
    </div>
  );
}

export default page;
