"use client";

import React, { useState, useRef } from "react";
import { But<PERSON> } from "../ui/button";
import { Plus, FolderPlus, Upload } from "lucide-react";
import FileUpload from "../icons/FileUpload";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdownMenu";
import { toast } from "sonner";
import FolderModal from "./FolderModal";
import useAddItemToTeamVault from "@/services/teamVault/addItem";

function AddNewItem() {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [folderModalOpen, setFolderModalOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { mutate, isPending: loading } = useAddItemToTeamVault(onSuccess);
  const handleFileUpload = () => {
    // Trigger the hidden file input to open system file dialog
    fileInputRef.current?.click();
    setDropdownOpen(false);
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Handle the selected file here
      console.log("Selected file:", file);
      mutate({
        mimeType: file.type,
        name: file.name,
        file,
      });

      // TODO: Add your file upload logic here
      // For example: upload to S3, save to database, etc.

      // Reset the input value to allow selecting the same file again
      event.target.value = "";
    }
  };

  // Open the folder creation modal
  const handleNewFolder = () => {
    setFolderModalOpen(true);
    setDropdownOpen(false);
  };

  // Handle folder creation submission
  const handleFolderSubmit = async (data: { folderName: string }) => {
    try {
      mutate({
        mimeType: "folder",
        name: data.folderName,
      });
    } catch (error) {
      console.error("Error creating folder:", error);
      toast.error("Failed to create folder");
    }
  };

  // Callback for successful item addition
  function onSuccess() {
    setFolderModalOpen(false);
    setDropdownOpen(false);
    toast.success("Item added successfully");
  }
  return (
    <div>
      <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
        <DropdownMenuTrigger asChild>
          <Button className="gap-x-2 pl-4 pr-3">
            New
            <Plus className="size-[22px] stroke-[2.5px]" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[160px] mt-2">
          <DropdownMenuItem
            onClick={handleFileUpload}
            className="gap-x-2 cursor-pointer"
          >
            <FileUpload className="size-4" />
            <span className="text-sm">File upload</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={handleNewFolder}
            className="gap-x-2 cursor-pointer"
          >
            <FolderPlus className="size-4 text-sm" />
            <span className="text-sm">New folder</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Hidden file input for system file dialog */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf,audio/*,video/*,image/*"
        onChange={handleFileChange}
        className="hidden"
      />

      {/* Folder creation modal */}
      <FolderModal
        open={folderModalOpen}
        onOpenChange={setFolderModalOpen}
        title="Create a new folder"
        buttonText="Create"
        onSubmit={handleFolderSubmit}
        loading={loading}
      />
    </div>
  );
}

export default AddNewItem;
