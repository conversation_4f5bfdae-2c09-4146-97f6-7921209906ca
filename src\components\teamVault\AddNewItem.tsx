"use client";

import React, { useState } from "react";
import { But<PERSON> } from "../ui/button";
import { Plus, FolderPlus, Upload } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdownMenu";

function AddNewItem() {
  const [open, setOpen] = useState(false);

  const handleFileUpload = () => {
    // Handle file upload logic here
    setOpen(false);
  };

  const handleNewFolder = () => {
    // Handle new folder creation logic here
    setOpen(false);
  };

  return (
    <div>
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger asChild>
          <Button className="gap-x-2 pl-4 pr-3">
            New
            <Plus className="size-[22px] stroke-[2.5px]" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[160px]">
          <DropdownMenuItem
            onClick={handleFileUpload}
            className="gap-x-2 cursor-pointer"
          >
            <Upload className="size-4" />
            File upload
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={handleNewFolder}
            className="gap-x-2 cursor-pointer"
          >
            <FolderPlus className="size-4" />
            New Folder
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

export default AddNewItem;
