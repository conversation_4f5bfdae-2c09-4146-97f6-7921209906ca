"use client";

import React, { useState, useRef } from "react";
import { Button } from "../ui/button";
import { Plus, FolderPlus, Upload } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdownMenu";
import { toast } from "sonner";
import FolderModal from "./FolderModal";

function AddNewItem() {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [folderModalOpen, setFolderModalOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = () => {
    // Trigger the hidden file input to open system file dialog
    fileInputRef.current?.click();
    setDropdownOpen(false);
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Handle the selected file here
      console.log("Selected file:", file);
      toast.success(`File "${file.name}" selected successfully!`);

      // TODO: Add your file upload logic here
      // For example: upload to S3, save to database, etc.

      // Reset the input value to allow selecting the same file again
      event.target.value = "";
    }
  };

  const handleNewFolder = () => {
    // Open the folder creation modal
    setFolderModalOpen(true);
    setDropdownOpen(false);
  };

  return (
    <div>
      <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
        <DropdownMenuTrigger asChild>
          <Button className="gap-x-2 pl-4 pr-3">
            New
            <Plus className="size-[22px] stroke-[2.5px]" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[160px] mt-2">
          <DropdownMenuItem
            onClick={handleFileUpload}
            className="gap-x-2 cursor-pointer"
          >
            <Upload className="size-4" />
            <span className="text-sm">File upload</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={handleNewFolder}
            className="gap-x-2 cursor-pointer"
          >
            <FolderPlus className="size-4 text-sm" />
            <span className="text-sm">New folder</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Hidden file input for system file dialog */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf,audio/*,video/*,image/*"
        onChange={handleFileChange}
        className="hidden"
      />

      {/* Folder creation modal */}
      <FolderModal open={folderModalOpen} onOpenChange={setFolderModalOpen} />
    </div>
  );
}

export default AddNewItem;
