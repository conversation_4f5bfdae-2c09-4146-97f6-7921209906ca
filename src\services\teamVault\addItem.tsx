import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";

import api from "@/lib/api-client";

const addItemToTeamVault = async (body: {
  mimeType: string;
  name: string;
  file?: File;
}) => {
  const { data } = await api({
    url: `/architect-team/items`,
    method: "POST",
    data: body,
  });
  return data;
};

const useAddItemToTeamVault = (onSuccess?: () => void) => {
  return useMutation({
    mutationFn: addItemToTeamVault,
    onSuccess: () => {
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to add item to team vault",
      );
    },
  });
};

export default useAddItemToTeamVault;
