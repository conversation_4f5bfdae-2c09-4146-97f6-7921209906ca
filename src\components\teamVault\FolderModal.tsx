"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { X } from "lucide-react";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

// Schema for folder creation form
const folderSchema = z.object({
  folderName: z
    .string()
    .min(1, "Folder name is required")
    .max(50, "Folder name must be less than 50 characters")
    .regex(/^[^<>:"/\\|?*]+$/, "Folder name contains invalid characters"),
});

type FolderFormData = z.infer<typeof folderSchema>;

interface FolderModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

function FolderModal({ open, onOpenChange }: FolderModalProps) {
  const [isCreating, setIsCreating] = useState(false);

  const form = useForm<FolderFormData>({
    resolver: zodResolver(folderSchema),
    defaultValues: {
      folderName: "",
    },
  });

  const onSubmit = async (data: FolderFormData) => {
    setIsCreating(true);

    try {
      // TODO: Add your folder creation logic here
      console.log("Creating folder:", data.folderName);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast.success(`Folder "${data.folderName}" created successfully!`);

      // Reset form and close modal
      form.reset();
      onOpenChange(false);
    } catch (error) {
      console.error("Error creating folder:", error);
      toast.error("Failed to create folder. Please try again.");
    } finally {
      setIsCreating(false);
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[400px] p-6">
        <DialogTitle className="flex items-center justify-between">
          Create a new folder
          <DialogClose asChild>
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={handleClose}
              className="h-6 w-6 p-0 hover:bg-transparent"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogClose>
        </DialogTitle>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="folderName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-neutrals-G600">
                    Folder name
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Enter folder name"
                      className="w-full"
                      disabled={isCreating}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end">
              <Button
                type="submit"
                loading={isCreating}
                disabled={isCreating}
                className="px-6"
              >
                {isCreating ? "Creating..." : "Create"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

export default FolderModal;
