"use client";

import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { X } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { folderSchema } from "@/schema/folder";
import { FolderModalProps, FolderFormData } from "@/types/TeamVault";

function FolderModal({
  open,
  onOpenChange,
  title,
  buttonText,
  initialValue = "",
  loading,
  onSubmit,
}: FolderModalProps) {
  const form = useForm<FolderFormData>({
    resolver: zodResolver(folderSchema),
    defaultValues: {
      folderName: initialValue,
    },
  });

  // Update form when initialValue changes (for edit mode)
  useEffect(() => {
    form.reset({ folderName: initialValue });
  }, [initialValue, form]);

  const handleSubmit = async (data: FolderFormData) => {
    try {
      onSubmit(data);

      // Reset form and close modal on success
      form.reset();
      onOpenChange(false);
    } catch (error) {
      console.error("Error submitting form:", error);
      // Error handling is done in the parent component's onSubmit
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className=" ">
        <DialogTitle className="flex items-center justify-between">
          {title}
          <DialogClose asChild>
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={handleClose}
              className="h-8 w-8 p-0 hover:bg-transparent"
            >
              <X className="h-6 w-6" />
            </Button>
          </DialogClose>
        </DialogTitle>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            <FormField
              control={form.control}
              name="folderName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-neutrals-G600">
                    Folder name
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Enter folder name"
                      className="w-full"
                      disabled={loading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end">
              <Button
                type="submit"
                loading={loading}
                disabled={loading}
                className="px-6"
              >
                {buttonText}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

export default FolderModal;
