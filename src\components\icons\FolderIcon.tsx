const FolderIcon = () => {
  return (
    <div className="group max-w-[250px] cursor-pointer">
      <svg
        width="200"
        height="182"
        viewBox="0 0 200 182"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="w-full h-full"
      >
        <g filter="url(#filter0_dddd_3490_9866)">
          <path
            d="M22.3916 3.41211H57.5293C61.339 3.41211 64.7788 5.69289 66.2617 9.20215L69.5674 17.0254C71.1792 20.8398 74.9185 23.3184 79.0596 23.3184H182.608C187.844 23.3186 192.088 27.5634 192.088 32.7988V83.3887C192.088 88.624 187.844 92.8679 182.608 92.8682H22.3926C17.1571 92.8682 12.9123 88.6241 12.9121 83.3887V12.8926C12.9121 7.65713 17.1562 3.41237 22.3916 3.41211Z"
            fill="#E4EEFD"
            stroke="#E4EEFD"
            strokeWidth="0.824341"
            className="transition-transform duration-300 group-hover:-translate-y-1"
          />
          <rect
            x="12.5"
            y="50.3867"
            width="180"
            height="90.2804"
            rx="9.89209"
            fill="#B4D1F9"
            className="transition-colors duration-300 group-hover:fill-[#9BC2F6]"
          />
        </g>
        <defs>
          <filter
            id="filter0_dddd_3490_9866"
            x="0.95923"
            y="0.526978"
            width="203.082"
            height="181.358"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dy="1.64868" />
            <feGaussianBlur stdDeviation="2.06085" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0.619608 0 0 0 0 0.619608 0 0 0 0 0.619608 0 0 0 0.1 0"
            />
            <feBlend
              mode="normal"
              in2="BackgroundImageFix"
              result="effect1_dropShadow_3490_9866"
            />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dy="7.41907" />
            <feGaussianBlur stdDeviation="3.70953" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0.619608 0 0 0 0 0.619608 0 0 0 0 0.619608 0 0 0 0.05 0"
            />
            <feBlend
              mode="normal"
              in2="effect1_dropShadow_3490_9866"
              result="effect2_dropShadow_3490_9866"
            />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dy="16.4868" />
            <feGaussianBlur stdDeviation="4.94604" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0.619608 0 0 0 0 0.619608 0 0 0 0 0.619608 0 0 0 0.05 0"
            />
            <feBlend
              mode="normal"
              in2="effect2_dropShadow_3490_9866"
              result="effect3_dropShadow_3490_9866"
            />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dy="29.6763" />
            <feGaussianBlur stdDeviation="5.77038" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0.619608 0 0 0 0 0.619608 0 0 0 0 0.619608 0 0 0 0.01 0"
            />
            <feBlend
              mode="normal"
              in2="effect3_dropShadow_3490_9866"
              result="effect4_dropShadow_3490_9866"
            />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect4_dropShadow_3490_9866"
              result="shape"
            />
          </filter>
        </defs>
      </svg>
    </div>
  );
};
export default FolderIcon;
