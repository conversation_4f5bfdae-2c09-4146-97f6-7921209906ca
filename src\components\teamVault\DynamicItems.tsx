import { getFileCategory } from "@/lib/utils";
import { TeamVaultItem } from "@/types/TeamVault";
import FolderIcon from "../icons/FolderIcon";
import PdfIcon from "../icons/Pdf";
import { Ellipsis } from "lucide-react";

function DynamicItems({ item }: { item: TeamVaultItem }) {
  const filetype = getFileCategory(item.mimeType);
  if (filetype === "folder") {
    return (
      <div className="cursor-pointer">
        <div className="flex flex-col  ">
          <FolderIcon />

          <div className="flex justify-between px-3 mt-5">
            <div className="flex flex-col gap-y-1">
              <p className="text-sm font-medium text-neutrals-G900 truncate max-w-[120px]">
                {item.name}
              </p>
              <p className="text-xs text-neutrals-G600">
                {item.itemsCount} items
              </p>
            </div>
            <button className="p-1 :bg-gray-100 rounded mb-auto">
              <Ellipsis className="size-4 text-neutrals-G600" />
            </button>
          </div>
        </div>
      </div>
    );
  }
  // File item rendering
  const renderFileIcon = () => {
    switch (filetype) {
      case "docs":
        return (
          <div className="w-20 h-36 bg-gray-200 rounded flex items-center justify-center">
            <span className="text-xs text-gray-600">Docs</span>
          </div>
        );
      case "pdf":
        return (
          <div className="flex justify-center">
            <PdfIcon />
          </div>
        );
      case "image":
        return (
          <div className="w-20 h-36 bg-gray-200 rounded flex items-center justify-center">
            <span className="text-xs text-gray-600">IMG</span>
          </div>
        );
      case "video":
        return (
          <div className="w-20 h-36 bg-gray-200 rounded flex items-center justify-center">
            <span className="text-xs text-gray-600">VID</span>
          </div>
        );
      case "audio":
        return (
          <div className="w-20 h-36 bg-gray-200 rounded flex items-center justify-center">
            <span className="text-xs text-gray-600">AUD</span>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className=" cursor-pointer">
      <div className="flex flex-col ">
        {renderFileIcon()}

        <div className="flex justify-between px-3 w-full mt-8">
          <div className="flex flex-col gap-y-1">
            <p className="text-sm font-medium text-neutrals-G900 truncate max-w-[120px]">
              {item.name}
            </p>
            <p className="text-xs text-neutrals-G600">
              Uploaded on
              {new Date(item.uploadedAt).toLocaleDateString("en-US", {
                month: "short",
                day: "numeric",
                year: "numeric",
              })}
            </p>
          </div>
          <button className="p-1 :bg-gray-100 rounded mb-auto">
            <Ellipsis className="size-4 text-neutrals-G600" />
          </button>
        </div>
      </div>
    </div>
  );
}

export default DynamicItems;
