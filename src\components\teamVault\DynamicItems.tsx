import { getFileCategory } from "@/lib/utils";
import { TeamVaultItem } from "@/types/TeamVault";
import FolderIcon from "../icons/FolderIcon";
import PdfIcon from "../icons/Pdf";
import { EllipsisVertical } from "lucide-react";

function DynamicItems({ item }: { item: TeamVaultItem }) {
  const filetype = getFileCategory(item.mimeType);

  // Check if item is a folder (has itemsCount property)
  const isFolder = item.itemsCount !== undefined;

  if (isFolder) {
    return (
      <div className="relative group cursor-pointer">
        <div className="flex flex-col items-center">
          <FolderIcon />
          <div className="mt-2 text-center">
            <p className="text-sm font-medium text-neutrals-G900 truncate max-w-[120px]">
              {item.name}
            </p>
            <p className="text-xs text-neutrals-G600">
              {item.itemsCount} items
            </p>
          </div>
        </div>

        {/* Three dots menu for folder */}
        <button className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-gray-100 rounded">
          <EllipsisVertical className="size-4 text-neutrals-G600" />
        </button>
      </div>
    );
  }

  // File item rendering
  const renderFileIcon = () => {
    switch (filetype) {
      case "pdf":
        return <PdfIcon className="w-16 h-16" />;
      case "image":
        return (
          <div className="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
            <span className="text-xs text-gray-600">IMG</span>
          </div>
        );
      case "video":
        return (
          <div className="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
            <span className="text-xs text-gray-600">VID</span>
          </div>
        );
      case "audio":
        return (
          <div className="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
            <span className="text-xs text-gray-600">AUD</span>
          </div>
        );
      default:
        return (
          <div className="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
            <span className="text-xs text-gray-600">FILE</span>
          </div>
        );
    }
  };

  return (
    <div className="relative group cursor-pointer">
      <div className="flex flex-col items-center">
        {renderFileIcon()}
        <div className="mt-2 text-center">
          <p className="text-sm font-medium text-neutrals-G900 truncate max-w-[120px]">
            {item.name}
          </p>
          <p className="text-xs text-neutrals-G600">
            Uploaded on{" "}
            {new Date(item.uploadedAt).toLocaleDateString("en-US", {
              month: "short",
              day: "numeric",
              year: "numeric",
            })}
          </p>
        </div>
      </div>

      {/* Three dots menu for file */}
      <button className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-gray-100 rounded">
        <EllipsisVertical className="size-4 text-neutrals-G600" />
      </button>
    </div>
  );
}

export default DynamicItems;
