"use client";
import { getFileCategory } from "@/lib/utils";
import { TeamVaultFolder, TeamVaultItem } from "@/types/TeamVault";
import FolderIcon from "../icons/FolderIcon";
import PdfIcon from "../icons/Pdf";
import { Ellipsis, Trash2 } from "lucide-react";
import { useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdownMenu";
import FolderModal from "./FolderModal";
import Edit2 from "../icons/Edit2";
import DeleteModal from "./DeleteModal";
import Image from "next/image";

function DynamicItems({
  item,
  handlePathChange,
}: {
  item: TeamVaultItem | TeamVaultFolder;
  handlePathChange: (newPath: string) => void;
}) {
  const filetype =
    item.type === "folder"
      ? "folder"
      : getFileCategory((item as TeamVaultItem).s3?.mimeType);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [folderModalOpen, setFolderModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const handleFolderSubmit = async (data: { folderName: string }) => {
    try {
      // mutate({
      //   mimeType: "folder",
      //   name: data.folderName,
      // });
    } catch (error) {
      console.error("Error creating folder:", error);
      // toast.error("Failed to create folder");
    }
  };
  const handleEdit = () => {
    setFolderModalOpen(true);
    setDropdownOpen(false);
  };
  const handleDelete = () => {
    setDeleteModalOpen(true);
    setDropdownOpen(false);
  };

  const handleFolderClick = () => {
    handlePathChange(item._id || "");
  };

  if (filetype === "folder") {
    return (
      <>
        <div className="cursor-pointer">
          <div className="flex flex-col justify-center ">
            <div onClick={handleFolderClick}>
              <FolderIcon />
            </div>
            <div className="flex justify-between px-3 mt-5">
              <div className="flex flex-col gap-y-1">
                <p className="text-sm font-medium text-neutrals-G900 truncate max-w-[150px]">
                  {item.name}
                </p>
                {item.itemsCount && (
                  <p className="text-xs text-neutrals-G600">
                    {item.itemsCount} items
                  </p>
                )}
              </div>

              <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
                <DropdownMenuTrigger asChild>
                  <button className="p-1 hover:bg-gray-100 rounded mb-auto">
                    <Ellipsis className="size-4 text-neutrals-G600" />
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[120px]">
                  <DropdownMenuItem
                    onClick={handleEdit}
                    className="gap-x-2 cursor-pointer"
                  >
                    <Edit2 />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleDelete}
                    className="gap-x-2 cursor-pointer"
                  >
                    <Trash2 className="size-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {folderModalOpen && (
          <FolderModal
            open={folderModalOpen}
            onOpenChange={setFolderModalOpen}
            title="Edit folder"
            buttonText="update"
            onSubmit={handleFolderSubmit}
            loading={false}
            initialValue={item.name} // Pass the current folder name
          />
        )}
        {deleteModalOpen && (
          <DeleteModal
            description="Do you wish to delete this folder?"
            open={deleteModalOpen}
            onOpenChange={setDeleteModalOpen}
            data={item}
            onSubmit={() => {
              // Handle delete logic here
            }}
            isPending={false} // Replace with actual loading state if needed
          />
        )}
      </>
    );
  }
  // File item rendering
  const renderFileIcon = () => {
    switch (filetype) {
      case "docs":
        return (
          <div className="w-20 h-36 bg-gray-200 rounded flex items-center justify-center">
            <span className="text-xs text-gray-600">Docs</span>
          </div>
        );
      case "pdf":
        return (
          <div className="flex justify-center">
            <PdfIcon />
          </div>
        );
      case "image":
        return (
          <div className="bg-gray-200 rounded  flex items-center justify-center">
            <Image
              src={(item as TeamVaultItem).s3?.url}
              alt={item.name}
              className="w-full h-full object-cover rounded"
              width={120}
              height={144}
            />
          </div>
        );
      case "video":
        return (
          <div className="w-20 h-36 bg-gray-200 rounded flex items-center justify-center">
            <span className="text-xs text-gray-600">VID</span>
          </div>
        );
      case "audio":
        return (
          <div className="w-20 h-36 bg-gray-200 rounded flex items-center justify-center">
            <span className="text-xs text-gray-600">AUD</span>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <>
      <div className="flex flex-col ">
        <div className="cursor-pointer">{renderFileIcon()}</div>

        <div className="flex justify-between px-3 w-full mt-8">
          <div className="flex flex-col gap-y-1">
            <p className="text-sm font-medium text-neutrals-G900 truncate max-w-[150px]">
              {item.name}
            </p>
            <p className="text-xs text-neutrals-G600">
              Uploaded on
              {new Date(item.createdAt).toLocaleDateString("en-US", {
                month: "short",
                day: "numeric",
                year: "numeric",
              })}
            </p>
          </div>

          <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <button className="p-1 hover:bg-gray-100 rounded mb-auto">
                <Ellipsis className="size-4 text-neutrals-G600" />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[120px]">
              <DropdownMenuItem
                onClick={handleDelete}
                className="gap-x-2 cursor-pointer"
              >
                <Trash2 className="size-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      {deleteModalOpen && (
        <DeleteModal
          description="Do you wish to delete this file?"
          open={deleteModalOpen}
          onOpenChange={setDeleteModalOpen}
          data={item}
          onSubmit={() => {
            // Handle delete logic here
          }}
          isPending={false}
        />
      )}
    </>
  );
}

export default DynamicItems;
